# @Log 注解使用说明

## 功能介绍

@Log 注解用于监听 Controller 接口的访问日志，可以记录以下信息：
- 请求IP地址和端口
- 用户名（从请求参数或请求头获取）
- 请求时间
- 请求方法（GET、POST等）
- 请求URL
- 请求参数
- 返回结果
- 方法执行时间
- User-Agent信息

## 使用方式

### 1. 在方法上使用

```java
@RestController
public class UserController {
    
    @Log("用户登录接口")
    @PostMapping("/login")
    public Object login(@RequestParam String username, @RequestParam String password) {
        // 登录逻辑
        return "登录成功";
    }
    
    @Log(value = "获取用户信息", logParams = true, logResult = true, logTime = true)
    @GetMapping("/user/{id}")
    public Object getUserInfo(@PathVariable Long id) {
        // 获取用户信息逻辑
        return "用户信息";
    }

    @Log(value = "深度监控查询", isDeep = true, logParams = true, logResult = true)
    @GetMapping("/user/{id}/detail")
    public Object getUserDetail(@PathVariable Long id) {
        // 这个方法会监控所有SQL查询的执行时间
        return userService.findById(id);
    }
}
```

### 2. 在类上使用

```java
@RestController
@Log("用户管理相关接口")
public class UserController {
    
    @GetMapping("/users")
    public Object getUsers() {
        // 该方法会被记录日志
        return "用户列表";
    }
    
    @PostMapping("/users")
    public Object createUser() {
        // 该方法也会被记录日志
        return "创建用户成功";
    }
}
```

### 3. 在Application类上使用（全局监听）

```java
@SpringBootApplication
@EnableAspectJAutoProxy
@Log("全局Controller访问日志监听")
public class DemoApplication {
    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);
    }
}
```

当在Application类上添加@Log注解时，所有Controller的方法都会被监听记录日志。

## 注解参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | "" | 日志描述信息 |
| logParams | boolean | true | 是否记录请求参数 |
| logResult | boolean | true | 是否记录返回结果 |
| logTime | boolean | true | 是否记录执行时间 |
| isDeep | boolean | false | 是否启用深度监控，分析JDBC查询时间 |

## 日志输出格式

```
=== 接口访问日志 ===
请求时间: 2024-01-01T10:30:00
请求IP: *************
请求端口: 8080
用户名: admin
请求方法: POST
请求URL: http://localhost:8080/login
类名: testController
方法名: login
描述: 用户登录接口
请求参数: ["admin","123456"]
返回结果: "登录成功"
执行时间: 15ms
User-Agent: Mozilla/5.0...
--- SQL查询详情 ---
SQL查询总数: 2
SQL总执行时间: 8ms
[SQL-1] 类型: SELECT, 耗时: 3ms, 影响行数: 1
    SQL: SELECT * FROM users WHERE username = ?
    参数: ['admin']
[SQL-2] 类型: UPDATE, 耗时: 5ms, 影响行数: 1
    SQL: UPDATE users SET last_login = ? WHERE id = ?
    参数: ['2024-01-01 10:30:00', 1]
--- SQL查询结束 ---
==================
```

## 测试方法

1. 启动应用程序
2. 访问以下接口进行测试：
   - GET `/hello` - 全局监听的接口
   - POST `/login?username=admin&password=123456` - 带@Log注解的登录接口
   - GET `/user/123?detail=full` - 带@Log注解的用户信息接口
   - GET `/test?name=张三` - 全局监听的测试接口

3. 查看控制台日志输出

## 扩展功能

可以通过修改 `LogService` 类来扩展日志存储功能：
- 保存到数据库
- 保存到文件
- 发送到日志收集系统（如ELK）
- 异步处理日志

## 注意事项

1. 确保项目中已添加 `spring-boot-starter-aop` 依赖
2. 在Application类上添加 `@EnableAspectJAutoProxy` 注解启用AOP
3. 日志记录是同步的，如果需要高性能可以考虑异步处理
4. 大量数据的请求参数和返回结果可能会影响性能，可以通过注解参数控制是否记录
