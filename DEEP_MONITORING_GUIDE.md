# @Log 深度监控功能使用指南

## 功能介绍

当@Log注解的`isDeep`参数设置为`true`时，系统会自动监控该请求过程中所有的JDBC查询，包括：

- SQL语句内容
- 查询参数
- 执行时间
- 影响行数
- 查询类型（SELECT、INSERT、UPDATE、DELETE）
- 异常信息（如果有）

## 使用示例

### 1. 启用深度监控

```java
@Log(value = "深度监控查询用户", isDeep = true)
@GetMapping("/users/{id}")
public User getUserById(@PathVariable Long id) {
    return userService.findById(id);
}
```

### 2. 复杂查询监控

```java
@Log(value = "深度监控复杂查询", isDeep = true, logParams = true, logResult = true)
@PostMapping("/users/batch")
public String batchOperation(@RequestParam String username) {
    // 会执行多个SQL查询
    User user = userService.findByUsername(username);
    if (user == null) {
        user = new User(username, username + "@example.com", 25);
        userService.save(user);
    }
    int totalUsers = userService.count();
    return "操作完成，总用户数: " + totalUsers;
}
```

## 测试接口

启动应用后，可以通过以下接口测试深度监控功能：

### 1. 普通查询对比
```bash
# 普通查询（不监控SQL）
curl "http://localhost:8080/demo/normal-db?id=1"

# 深度监控查询（监控SQL）
curl "http://localhost:8080/demo/deep-db?id=1"
```

### 2. 复杂查询监控
```bash
# 复杂查询（多个SQL）
curl "http://localhost:8080/demo/deep-complex?id=1"
```

### 3. 用户管理接口
```bash
# 查询用户（深度监控）
curl "http://localhost:8080/users/1"

# 复杂查询（多个SQL）
curl "http://localhost:8080/users/1/stats"

# 批量操作（多个SQL）
curl -X POST "http://localhost:8080/users/batch?username=newuser"

# 创建用户
curl -X POST "http://localhost:8080/users" \
     -H "Content-Type: application/json" \
     -d '{"username":"testuser","email":"<EMAIL>","age":25}'

# 更新用户
curl -X PUT "http://localhost:8080/users/1" \
     -H "Content-Type: application/json" \
     -d '{"username":"updated","email":"<EMAIL>","age":30}'

# 删除用户
curl -X DELETE "http://localhost:8080/users/1"
```

## 日志输出示例

### 普通日志（isDeep = false）
```
=== 接口访问日志 ===
请求时间: 2024-01-01T10:30:00.123
请求IP: 127.0.0.1
请求方法: GET
请求URL: http://localhost:8080/demo/normal-db
执行时间: 15ms
==================
```

### 深度监控日志（isDeep = true）
```
=== 接口访问日志 ===
请求时间: 2024-01-01T10:30:00.123
请求IP: 127.0.0.1
请求方法: GET
请求URL: http://localhost:8080/demo/deep-db
执行时间: 15ms
--- SQL查询详情 ---
SQL查询总数: 1
SQL总执行时间: 8ms
[SQL-1] 类型: SELECT, 耗时: 8ms, 影响行数: 1
    SQL: SELECT * FROM users WHERE id = ?
    参数: [1]
--- SQL查询结束 ---
==================
```

### 复杂查询日志
```
=== 接口访问日志 ===
请求时间: 2024-01-01T10:30:00.123
请求IP: 127.0.0.1
请求方法: POST
请求URL: http://localhost:8080/users/batch
参数: ["newuser"]
执行时间: 25ms
--- SQL查询详情 ---
SQL查询总数: 3
SQL总执行时间: 18ms
[SQL-1] 类型: SELECT, 耗时: 5ms, 影响行数: 0
    SQL: SELECT * FROM users WHERE username = ?
    参数: ['newuser']
[SQL-2] 类型: INSERT, 耗时: 8ms, 影响行数: 1
    SQL: INSERT INTO users(username, email, age) VALUES(?, ?, ?)
    参数: ['newuser', '<EMAIL>', 25]
[SQL-3] 类型: SELECT, 耗时: 5ms, 影响行数: 4
    SQL: SELECT COUNT(*) FROM users
    参数: []
--- SQL查询结束 ---
==================
```

## 性能分析

通过深度监控，您可以：

1. **识别慢查询**：查看每个SQL的执行时间
2. **优化查询次数**：发现N+1查询问题
3. **分析SQL性能**：对比不同查询的执行效率
4. **监控数据库压力**：统计总SQL执行时间占比

## 注意事项

1. **性能影响**：深度监控会有轻微的性能开销，建议在开发和测试环境使用
2. **内存使用**：监控信息存储在ThreadLocal中，请求结束后会自动清理
3. **日志大小**：复杂查询可能产生较大的日志，注意日志存储空间
4. **并发安全**：使用ThreadLocal确保多线程环境下的数据隔离

## 扩展功能

可以通过以下方式扩展深度监控功能：

1. **慢查询告警**：当SQL执行时间超过阈值时发送告警
2. **SQL性能统计**：收集SQL执行统计信息
3. **查询优化建议**：基于执行时间和查询模式提供优化建议
4. **可视化展示**：将SQL执行信息展示在监控面板上
