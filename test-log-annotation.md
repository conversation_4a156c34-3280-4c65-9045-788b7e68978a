# @Log注解测试指南

## 前置条件

1. 确保已添加必要的依赖到pom.xml：
   - spring-boot-starter-aop
   - aspectjweaver
   - aspectjrt

2. 确保Application类上有以下注解：
   - @EnableAspectJAutoProxy
   - @Log("全局Controller访问日志监听")

## 编译和运行

```bash
# 如果有Maven命令行工具
mvn clean compile
mvn spring-boot:run

# 或者在IDE中直接运行 demoApplication.main() 方法
```

## 测试用例

启动应用后，可以通过以下URL测试@Log注解功能：

### 1. 方法级别的@Log注解测试
```bash
curl "http://localhost:8080/demo/method-log?name=张三"
```
预期：控制台输出详细的访问日志，包括请求参数、返回结果、执行时间等。

### 2. 自定义日志参数测试
```bash
curl -X POST "http://localhost:8080/demo/custom-log" \
     -H "Content-Type: application/json" \
     -d "测试数据"
```
预期：记录请求参数和执行时间，但不记录返回结果。

### 3. 简化日志记录测试
```bash
curl "http://localhost:8080/demo/simple-log"
```
预期：只记录基本信息和执行时间，不记录请求参数和返回结果。

### 4. 全局监听测试
```bash
curl "http://localhost:8080/demo/global-log?message=全局测试"
```
预期：虽然方法上没有@Log注解，但因为Application类上有@Log注解，所以会记录日志。

### 5. 异常处理测试
```bash
# 正常执行
curl "http://localhost:8080/demo/error-log?throwError=false"

# 异常执行
curl "http://localhost:8080/demo/error-log?throwError=true"
```
预期：即使出现异常，也会记录访问日志，并在结果中显示异常信息。

### 6. 原有接口测试
```bash
# 测试原有的hello接口
curl "http://localhost:8080/hello"

# 测试登录接口
curl -X POST "http://localhost:8080/login?username=admin&password=123456"

# 测试用户信息接口
curl "http://localhost:8080/user/123?detail=full"

# 测试普通接口
curl "http://localhost:8080/test?name=测试用户"
```

## 预期日志格式

控制台应该输出类似以下格式的日志：

```
INFO  - 访问日志: 
=== 接口访问日志 ===
请求时间: 2024-01-01T10:30:00.123
请求IP: 127.0.0.1
请求端口: 8080
用户名: 未知
请求方法: GET
请求URL: http://localhost:8080/demo/method-log
类名: LogDemo
方法名: methodLevelLog
描述: 演示方法级别日志记录
请求参数: ["张三"]
返回结果: "Hello 张三! 这是方法级别的日志记录演示。"
执行时间: 5ms
User-Agent: curl/7.68.0
==================
```

## 故障排除

1. **@Aspect注解无法识别**
   - 确保pom.xml中包含aspectjweaver和aspectjrt依赖
   - 检查IDE是否正确导入了依赖

2. **AOP不生效**
   - 确保Application类上有@EnableAspectJAutoProxy注解
   - 确保LogAspect类上有@Component注解

3. **日志不输出**
   - 检查日志级别配置
   - 确保LogService中的log.info()能正常输出

4. **全局监听不生效**
   - 确保Application类上有@Log注解
   - 检查切点表达式是否正确匹配Controller类

## 自定义扩展

可以通过以下方式扩展@Log注解功能：

1. **修改LogService**：添加数据库存储、文件存储等功能
2. **修改LogAspect**：添加更多切点表达式，支持更多注解
3. **修改LogInfo**：添加更多字段，如请求头信息、响应状态码等
4. **添加异步处理**：使用@Async注解实现异步日志记录
