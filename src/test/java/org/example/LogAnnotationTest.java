package org.example;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * @Log注解功能测试
 */
@SpringBootTest
@SpringJUnitConfig
@AutoConfigureWebMvc
public class LogAnnotationTest {
    
    @Autowired
    private WebApplicationContext webApplicationContext;
    
    private MockMvc mockMvc;
    
    @Test
    public void testLogAnnotation() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        
        // 测试全局监听的接口（没有@Log注解的方法）
        System.out.println("=== 测试全局监听接口 ===");
        mockMvc.perform(get("/hello"))
                .andExpect(status().isOk());
        
        // 测试带有@Log注解的登录接口
        System.out.println("\n=== 测试带@Log注解的登录接口 ===");
        mockMvc.perform(post("/login")
                .param("username", "admin")
                .param("password", "123456"))
                .andExpect(status().isOk());
        
        // 测试带有@Log注解的用户信息接口
        System.out.println("\n=== 测试带@Log注解的用户信息接口 ===");
        mockMvc.perform(get("/user/123")
                .param("detail", "full"))
                .andExpect(status().isOk());
        
        // 测试全局监听的测试接口
        System.out.println("\n=== 测试全局监听的测试接口 ===");
        mockMvc.perform(get("/test")
                .param("name", "张三"))
                .andExpect(status().isOk());
    }
}
