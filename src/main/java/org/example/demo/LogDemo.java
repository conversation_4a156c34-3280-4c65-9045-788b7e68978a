package org.example.demo;

import org.example.annotation.Log;
import org.example.entity.User;
import org.example.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Log注解使用演示
 */
@RestController
@RequestMapping("/demo")
public class LogDemo {

    @Autowired
    private UserService userService;
    
    /**
     * 演示方法级别的@Log注解
     */
    @Log("演示方法级别日志记录")
    @GetMapping("/method-log")
    public String methodLevelLog(@RequestParam(required = false) String name) {
        return "Hello " + (name != null ? name : "World") + "! 这是方法级别的日志记录演示。";
    }
    
    /**
     * 演示自定义日志参数
     */
    @Log(value = "自定义参数演示", logParams = true, logResult = false, logTime = true)
    @PostMapping("/custom-log")
    public String customLog(@RequestBody String data) {
        return "处理数据: " + data;
    }
    
    /**
     * 演示不记录参数和结果的日志
     */
    @Log(value = "简化日志记录", logParams = false, logResult = false, logTime = true)
    @GetMapping("/simple-log")
    public String simpleLog() {
        return "这个方法只记录基本信息和执行时间";
    }
    
    /**
     * 这个方法没有@Log注解，但会被全局监听（因为Application类上有@Log注解）
     */
    @GetMapping("/global-log")
    public String globalLog(@RequestParam(required = false) String message) {
        return "全局监听: " + (message != null ? message : "默认消息");
    }
    
    /**
     * 演示异常情况下的日志记录
     */
    @Log("异常处理演示")
    @GetMapping("/error-log")
    public String errorLog(@RequestParam(required = false) boolean throwError) {
        if (throwError) {
            throw new RuntimeException("这是一个演示异常");
        }
        return "正常执行完成";
    }

    /**
     * 演示深度监控功能 - 不启用SQL监控
     */
    @Log(value = "普通数据库查询", logParams = true, logResult = true)
    @GetMapping("/normal-db")
    public User normalDbQuery(@RequestParam(required = false, defaultValue = "1") Long id) {
        return userService.findById(id);
    }

    /**
     * 演示深度监控功能 - 启用SQL监控
     */
    @Log(value = "深度监控数据库查询", isDeep = true, logParams = true, logResult = true)
    @GetMapping("/deep-db")
    public User deepDbQuery(@RequestParam(required = false, defaultValue = "1") Long id) {
        return userService.findById(id);
    }

    /**
     * 演示深度监控 - 复杂查询（多个SQL）
     */
    @Log(value = "深度监控复杂查询", isDeep = true, logParams = true, logResult = true)
    @GetMapping("/deep-complex")
    public String deepComplexQuery(@RequestParam(required = false, defaultValue = "1") Long id) {
        // 执行多个数据库查询
        User user = userService.findById(id);
        int totalUsers = userService.count();

        if (user != null) {
            return String.format("用户: %s, 总用户数: %d", user.getUsername(), totalUsers);
        } else {
            return "用户不存在，总用户数: " + totalUsers;
        }
    }
}
