package org.example.thread;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class main {
    public static void main(String[] args) throws InterruptedException {
        ThreadPoolExecutor executor = new ThreadPool(2,
                20,
                1000,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(50));
        for (int j = 0; j < 30; j++) {
            int finalJ = j;
            executor.execute(new Runnable() {
                public void run() {
                    for (int i = 0; i < 100; i++) {
                            //Thread.sleep(100);
                            if (i == 99){
                                System.out.println(Thread.currentThread().getName() + "=====" + finalJ + "========" + i);
                            }
                    }
                }
            });
        }
        //Thread.sleep(5000);
        //executor.shutdown();
        executor.shutdownNow();
        System.out.println("线程池关闭");
    }
}
