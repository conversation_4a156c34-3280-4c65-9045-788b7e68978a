package org.example;

import org.example.annotation.Log;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication
@EnableAspectJAutoProxy
@Log("全局Controller访问日志监听")
public class demoApplication {
    public static void main(String[] args) {
        SpringApplication.run(demoApplication.class, args);
    }
}
