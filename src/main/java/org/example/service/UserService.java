package org.example.service;

import org.example.entity.User;
import org.example.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户服务类
 */
@Service
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    public User findById(Long id) {
        return userMapper.findById(id);
    }
    
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }
    
    public List<User> findAll() {
        return userMapper.findAll();
    }
    
    public User save(User user) {
        if (user.getId() == null) {
            userMapper.insert(user);
        } else {
            userMapper.update(user);
        }
        return user;
    }
    
    public boolean deleteById(Long id) {
        return userMapper.deleteById(id) > 0;
    }
    
    public int count() {
        return userMapper.count();
    }
    
    /**
     * 复杂查询示例 - 会执行多个SQL
     */
    public User getUserWithStats(Long id) {
        // 查询用户信息
        User user = userMapper.findById(id);
        if (user != null) {
            // 查询总用户数
            int totalUsers = userMapper.count();
            // 可以在这里添加更多查询...
            
            // 模拟一些业务逻辑
            user.setEmail(user.getEmail() + " (总用户数: " + totalUsers + ")");
        }
        return user;
    }
}
