package org.example.service;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.example.entity.LogInfo;
import org.springframework.stereotype.Service;

/**
 * 日志服务类
 */
@Service
@Slf4j
public class LogService {
    
    private final Gson gson = new Gson();
    
    /**
     * 记录访问日志
     * @param logInfo 日志信息
     */
    public void saveLog(LogInfo logInfo) {
        try {
            // 格式化日志输出
            String logMessage = formatLogMessage(logInfo);
            log.info("访问日志: {}", logMessage);
            
            // 这里可以扩展为保存到数据库、文件或其他存储系统
            // 例如：logRepository.save(logInfo);
            
        } catch (Exception e) {
            log.error("记录访问日志失败", e);
        }
    }
    
    /**
     * 格式化日志消息
     * @param logInfo 日志信息
     * @return 格式化后的日志消息
     */
    private String formatLogMessage(LogInfo logInfo) {
        StringBuilder sb = new StringBuilder();
        sb.append("\n=== 接口访问日志 ===");
        sb.append("\n请求时间: ").append(logInfo.getRequestTime());
        sb.append("\n请求IP: ").append(logInfo.getIp());
        sb.append("\n请求端口: ").append(logInfo.getPort());
        sb.append("\n用户名: ").append(logInfo.getUsername() != null ? logInfo.getUsername() : "未知");
        sb.append("\n请求方法: ").append(logInfo.getMethod());
        sb.append("\n请求URL: ").append(logInfo.getUrl());
        sb.append("\n类名: ").append(logInfo.getClassName());
        sb.append("\n方法名: ").append(logInfo.getMethodName());
        
        if (logInfo.getDescription() != null && !logInfo.getDescription().isEmpty()) {
            sb.append("\n描述: ").append(logInfo.getDescription());
        }
        
        if (logInfo.getParams() != null && !logInfo.getParams().isEmpty()) {
            sb.append("\n请求参数: ").append(logInfo.getParams());
        }
        
        if (logInfo.getResult() != null && !logInfo.getResult().isEmpty()) {
            sb.append("\n返回结果: ").append(logInfo.getResult());
        }
        
        if (logInfo.getExecutionTime() != null) {
            sb.append("\n执行时间: ").append(logInfo.getExecutionTime()).append("ms");
        }
        
        if (logInfo.getUserAgent() != null && !logInfo.getUserAgent().isEmpty()) {
            sb.append("\nUser-Agent: ").append(logInfo.getUserAgent());
        }

        // 添加SQL查询信息（深度监控）
        if (logInfo.getSqlQueries() != null && !logInfo.getSqlQueries().isEmpty()) {
            sb.append("\n--- SQL查询详情 ---");
            sb.append("\nSQL查询总数: ").append(logInfo.getSqlCount());
            sb.append("\nSQL总执行时间: ").append(logInfo.getTotalSqlTime()).append("ms");

            for (int i = 0; i < logInfo.getSqlQueries().size(); i++) {
                var sqlQuery = logInfo.getSqlQueries().get(i);
                sb.append("\n[SQL-").append(i + 1).append("] ");
                sb.append("类型: ").append(sqlQuery.getQueryType());
                sb.append(", 耗时: ").append(sqlQuery.getExecutionTime()).append("ms");
                sb.append(", 影响行数: ").append(sqlQuery.getAffectedRows());
                sb.append("\n    SQL: ").append(sqlQuery.getSql());
                if (sqlQuery.getParameters() != null && !sqlQuery.getParameters().equals("[]")) {
                    sb.append("\n    参数: ").append(sqlQuery.getParameters());
                }
                if (!sqlQuery.getSuccess()) {
                    sb.append("\n    异常: ").append(sqlQuery.getErrorMessage());
                }
            }
            sb.append("\n--- SQL查询结束 ---");
        }

        sb.append("\n==================");

        return sb.toString();
    }
    
    /**
     * 将对象转换为JSON字符串
     * @param obj 对象
     * @return JSON字符串
     */
    public String toJson(Object obj) {
        try {
            if (obj == null) {
                return null;
            }
            return gson.toJson(obj);
        } catch (Exception e) {
            log.warn("对象转JSON失败: {}", e.getMessage());
            return obj.toString();
        }
    }
}
