package org.example.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.example.entity.SqlQueryInfo;
import org.example.monitor.SqlMonitor;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;

/**
 * MyBatis SQL拦截器
 * 用于监控SQL执行时间和参数
 */
@Slf4j
@Component
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class SqlInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 如果没有启用SQL监控，直接执行
        if (!SqlMonitor.isEnabled()) {
            return invocation.proceed();
        }

        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = null;
        if (invocation.getArgs().length > 1) {
            parameter = invocation.getArgs()[1];
        }

        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        Configuration configuration = mappedStatement.getConfiguration();
        
        String sql = formatSql(boundSql, parameter, configuration);
        String parameters = getParameterValue(boundSql, parameter, configuration);
        
        // 开始监控SQL
        SqlQueryInfo queryInfo = SqlMonitor.startQuery(sql, parameters);
        
        try {
            // 执行SQL
            Object result = invocation.proceed();
            
            // 计算影响行数
            Integer affectedRows = calculateAffectedRows(result);
            
            // 结束监控
            SqlMonitor.endQuery(queryInfo, affectedRows);
            
            return result;
            
        } catch (Exception e) {
            // 记录异常
            SqlMonitor.recordError(queryInfo, e.getMessage());
            throw e;
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(java.util.Properties properties) {
        // 可以在这里设置属性
    }

    /**
     * 格式化SQL语句
     */
    private String formatSql(BoundSql boundSql, Object parameterObject, Configuration configuration) {
        String sql = boundSql.getSql();
        if (sql == null || sql.length() == 0) {
            return "";
        }
        
        // 去除多余的空格和换行
        sql = sql.replaceAll("[\\s]+", " ").trim();
        return sql;
    }

    /**
     * 获取参数值
     */
    private String getParameterValue(BoundSql boundSql, Object parameterObject, Configuration configuration) {
        try {
            List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
            if (parameterMappings == null || parameterMappings.isEmpty()) {
                return "[]";
            }

            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            StringBuilder params = new StringBuilder("[");
            
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                params.append(getParameterValue(parameterObject));
            } else {
                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                for (int i = 0; i < parameterMappings.size(); i++) {
                    ParameterMapping parameterMapping = parameterMappings.get(i);
                    String propertyName = parameterMapping.getProperty();
                    if (metaObject.hasGetter(propertyName)) {
                        Object obj = metaObject.getValue(propertyName);
                        params.append(getParameterValue(obj));
                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        Object obj = boundSql.getAdditionalParameter(propertyName);
                        params.append(getParameterValue(obj));
                    }
                    if (i < parameterMappings.size() - 1) {
                        params.append(", ");
                    }
                }
            }
            params.append("]");
            return params.toString();
            
        } catch (Exception e) {
            log.warn("获取SQL参数失败: {}", e.getMessage());
            return "[]";
        }
    }

    /**
     * 格式化参数值
     */
    private String getParameterValue(Object obj) {
        if (obj == null) {
            return "null";
        }
        
        if (obj instanceof String) {
            return "'" + obj + "'";
        }
        
        if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            return "'" + formatter.format((Date) obj) + "'";
        }
        
        return obj.toString();
    }

    /**
     * 计算影响行数
     */
    private Integer calculateAffectedRows(Object result) {
        if (result == null) {
            return 0;
        }
        
        if (result instanceof Integer) {
            return (Integer) result;
        }
        
        if (result instanceof List) {
            return ((List<?>) result).size();
        }
        
        return 1;
    }
}
