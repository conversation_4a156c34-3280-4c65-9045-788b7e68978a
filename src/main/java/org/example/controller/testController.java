package org.example.controller;

import org.example.annotation.Log;
import org.example.filter.RequestHolder;
import org.springframework.web.bind.annotation.*;

@RestController
public class testController {

    @RequestMapping(value = "/hello", method = RequestMethod.GET)
    public Object hello() {
        RequestHolder.get();
        System.out.println(RequestHolder.getUserInfo());
        return "hello";
    }

    @Log(value = "用户登录接口", logParams = true, logResult = true, logTime = true)
    @GetMapping("/login")
    public Object login(@RequestParam String username, @RequestParam String password) {
        // 模拟登录逻辑
        if ("admin".equals(username) && "123456".equals(password)) {
            return "登录成功";
        }
        return "登录失败";
    }

    @Log(value = "获取用户信息", logParams = true, logResult = true, logTime = true)
    @GetMapping("/user/{id}")
    public Object getUserInfo(@PathVariable Long id, @RequestParam(required = false) String detail) {
        // 模拟获取用户信息
        return "用户ID: " + id + ", 详情: " + detail;
    }

    @GetMapping("/test")
    public Object test(@RequestParam(required = false) String name) {
        // 这个方法会被全局@Log注解监听（因为Application类上有@Log注解）
        return "测试接口，参数: " + name;
    }
}
