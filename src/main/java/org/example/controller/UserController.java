package org.example.controller;

import org.example.annotation.Log;
import org.example.entity.User;
import org.example.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器 - 演示深度监控功能
 */
@RestController
@RequestMapping("/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 普通查询 - 不启用深度监控
     */
    @Log("查询用户列表")
    @GetMapping
    public List<User> getAllUsers() {
        return userService.findAll();
    }
    
    /**
     * 深度监控查询 - 启用SQL监控
     */
    @Log(value = "深度监控查询用户", isDeep = true)
    @GetMapping("/{id}")
    public User getUserById(@PathVariable Long id) {
        return userService.findById(id);
    }
    
    /**
     * 深度监控复杂查询 - 会执行多个SQL
     */
    @Log(value = "深度监控复杂查询", isDeep = true, logParams = true, logResult = true)
    @GetMapping("/{id}/stats")
    public User getUserWithStats(@PathVariable Long id) {
        return userService.getUserWithStats(id);
    }
    
    /**
     * 深度监控创建用户
     */
    @Log(value = "深度监控创建用户", isDeep = true)
    @PostMapping
    public User createUser(@RequestBody User user) {
        return userService.save(user);
    }
    
    /**
     * 深度监控更新用户
     */
    @Log(value = "深度监控更新用户", isDeep = true)
    @PutMapping("/{id}")
    public User updateUser(@PathVariable Long id, @RequestBody User user) {
        user.setId(id);
        return userService.save(user);
    }
    
    /**
     * 深度监控删除用户
     */
    @Log(value = "深度监控删除用户", isDeep = true)
    @DeleteMapping("/{id}")
    public String deleteUser(@PathVariable Long id) {
        boolean deleted = userService.deleteById(id);
        return deleted ? "删除成功" : "删除失败";
    }
    
    /**
     * 批量操作演示 - 会产生多个SQL查询
     */
    @Log(value = "批量操作深度监控", isDeep = true, logParams = true)
    @PostMapping("/batch")
    public String batchOperation(@RequestParam String username) {
        // 先查询用户
        User existingUser = userService.findByUsername(username);
        
        if (existingUser == null) {
            // 创建新用户
            User newUser = new User(username, username + "@example.com", 25);
            userService.save(newUser);
            
            // 再次查询确认
            User savedUser = userService.findByUsername(username);
            return "创建用户成功，ID: " + savedUser.getId();
        } else {
            // 更新现有用户
            existingUser.setAge(existingUser.getAge() + 1);
            userService.save(existingUser);
            return "更新用户成功，新年龄: " + existingUser.getAge();
        }
    }
}
