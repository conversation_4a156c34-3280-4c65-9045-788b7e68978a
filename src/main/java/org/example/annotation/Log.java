package org.example.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日志注解
 * 用于监听Controller接口的访问日志
 * 可以加在方法上或类上
 * 如果加在Application类上，则监听所有Controller
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Log {

    /**
     * 日志描述
     */
    String value() default "";

    /**
     * 是否记录请求参数
     */
    boolean logParams() default true;

    /**
     * 是否记录返回结果
     */
    boolean logResult() default true;

    /**
     * 是否记录执行时间
     */
    boolean logTime() default true;

    /**
     * 是否启用深度监控
     * 当为true时，会分析本次请求的所有mapper（JDBC）查询时间
     */
    boolean isDeep() default false;
}
