package org.example.filter;
import lombok.extern.slf4j.Slf4j;
@Slf4j
public final class RequestHolder {

    private static final ThreadLocal<String> REQUESTHOLDER = new ThreadLocal<>();

    private RequestHolder() {
    }

    public static void add(final String name) {
        REQUESTHOLDER.set(name);
    }

    public static void remove() {
        REQUESTHOLDER.remove();
    }

    public static String getUserInfo() {
        return REQUESTHOLDER.get();
    }

    public static ThreadLocal<String> get() {
        return REQUESTHOLDER;
    }
}
