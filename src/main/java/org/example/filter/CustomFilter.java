package org.example.filter;

import javax.servlet.*;
import java.io.IOException;

public class CustomFilter implements Filter {
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        System.out.println(servletRequest);
        String name = servletRequest.getParameter("name");
        System.out.println(name);
        RequestHolder.add(name);
        filterChain.doFilter(servletRequest, servletResponse);
        RequestHolder.remove();
    }
}
