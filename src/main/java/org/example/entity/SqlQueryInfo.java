package org.example.entity;

import lombok.Data;

/**
 * SQL查询信息实体类
 */
@Data
public class SqlQueryInfo {
    
    /**
     * SQL语句
     */
    private String sql;
    
    /**
     * 参数
     */
    private String parameters;
    
    /**
     * 执行时间(毫秒)
     */
    private Long executionTime;
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 结束时间
     */
    private Long endTime;
    
    /**
     * 影响行数
     */
    private Integer affectedRows;
    
    /**
     * 查询类型 (SELECT, INSERT, UPDATE, DELETE)
     */
    private String queryType;
    
    /**
     * 是否成功执行
     */
    private Boolean success;
    
    /**
     * 异常信息（如果有）
     */
    private String errorMessage;
    
    public SqlQueryInfo() {
        this.startTime = System.currentTimeMillis();
        this.success = true;
    }
    
    public void markEnd() {
        this.endTime = System.currentTimeMillis();
        this.executionTime = this.endTime - this.startTime;
    }
    
    public void markError(String errorMessage) {
        this.success = false;
        this.errorMessage = errorMessage;
        markEnd();
    }
}
