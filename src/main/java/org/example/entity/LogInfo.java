package org.example.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 日志信息实体类
 */
@Data
public class LogInfo {
    
    /**
     * 请求IP
     */
    private String ip;
    
    /**
     * 请求端口
     */
    private Integer port;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 请求时间
     */
    private LocalDateTime requestTime;
    
    /**
     * 请求方法
     */
    private String method;
    
    /**
     * 请求URL
     */
    private String url;
    
    /**
     * 请求参数
     */
    private String params;
    
    /**
     * 返回结果
     */
    private String result;
    
    /**
     * 执行时间(毫秒)
     */
    private Long executionTime;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 日志描述
     */
    private String description;
    
    /**
     * User-Agent
     */
    private String userAgent;

    /**
     * SQL查询信息列表（深度监控时使用）
     */
    private List<SqlQueryInfo> sqlQueries;

    /**
     * 总SQL执行时间（毫秒）
     */
    private Long totalSqlTime;

    /**
     * SQL查询总数
     */
    private Integer sqlCount;
}
