package org.example.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.example.annotation.Log;
import org.example.entity.LogInfo;
import org.example.monitor.SqlMonitor;
import org.example.service.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 日志切面类
 * 用于拦截带有@Log注解的方法或类
 */
@Aspect
@Component
public class LogAspect {
    
    @Autowired
    private LogService logService;
    
    /**
     * 定义切点：拦截带有@Log注解的方法
     */
    @Pointcut("@annotation(org.example.annotation.Log)")
    public void logMethodPointcut() {}
    
    /**
     * 定义切点：拦截带有@Log注解的类中的所有方法
     */
    @Pointcut("@within(org.example.annotation.Log)")
    public void logClassPointcut() {}
    
    /**
     * 定义切点：拦截所有Controller类的方法（当Application类上有@Log注解时）
     */
    @Pointcut("execution(* org.example.controller..*(..))")
    public void controllerPointcut() {}
    
    /**
     * 环绕通知：记录方法执行日志
     */
    @Around("logMethodPointcut() || logClassPointcut() || (controllerPointcut() && @within(org.springframework.web.bind.annotation.RestController))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取@Log注解
        Log logAnnotation = getLogAnnotation(method, joinPoint.getTarget().getClass());
        
        // 如果没有@Log注解且不是在Application类上标注的全局监听，则不记录日志
        if (logAnnotation == null && !isGlobalLogEnabled()) {
            return joinPoint.proceed();
        }
        
        // 创建日志信息对象
        LogInfo logInfo = new LogInfo();

        // 检查是否启用深度监控
        boolean isDeepMonitoring = logAnnotation != null && logAnnotation.isDeep();

        try {
            // 启用SQL监控（如果需要）
            if (isDeepMonitoring) {
                SqlMonitor.enable();
            }

            // 获取HTTP请求信息
            HttpServletRequest request = getHttpServletRequest();
            if (request != null) {
                fillRequestInfo(logInfo, request);
            }

            // 填充方法信息
            fillMethodInfo(logInfo, joinPoint, logAnnotation);

            // 记录请求参数
            if (logAnnotation == null || logAnnotation.logParams()) {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    logInfo.setParams(logService.toJson(Arrays.asList(args)));
                }
            }
            
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 计算执行时间
            long endTime = System.currentTimeMillis();
            if (logAnnotation == null || logAnnotation.logTime()) {
                logInfo.setExecutionTime(endTime - startTime);
            }
            
            // 记录返回结果
            if (logAnnotation == null || logAnnotation.logResult()) {
                logInfo.setResult(logService.toJson(result));
            }

            // 收集SQL监控信息（如果启用了深度监控）
            if (isDeepMonitoring) {
                logInfo.setSqlQueries(SqlMonitor.getQueries());
                logInfo.setTotalSqlTime(SqlMonitor.getTotalSqlTime());
                logInfo.setSqlCount(SqlMonitor.getSqlCount());
            }

            // 保存日志
            logService.saveLog(logInfo);

            return result;
            
        } catch (Exception e) {
            // 即使出现异常也要记录日志
            long endTime = System.currentTimeMillis();
            logInfo.setExecutionTime(endTime - startTime);
            logInfo.setResult("执行异常: " + e.getMessage());

            // 收集SQL监控信息（如果启用了深度监控）
            if (isDeepMonitoring) {
                logInfo.setSqlQueries(SqlMonitor.getQueries());
                logInfo.setTotalSqlTime(SqlMonitor.getTotalSqlTime());
                logInfo.setSqlCount(SqlMonitor.getSqlCount());
            }

            logService.saveLog(logInfo);

            throw e;
        } finally {
            // 清理SQL监控
            if (isDeepMonitoring) {
                SqlMonitor.disable();
            }
        }
    }
    
    /**
     * 获取@Log注解
     */
    private Log getLogAnnotation(Method method, Class<?> targetClass) {
        // 先检查方法上的注解
        Log logAnnotation = method.getAnnotation(Log.class);
        if (logAnnotation != null) {
            return logAnnotation;
        }
        
        // 再检查类上的注解
        return targetClass.getAnnotation(Log.class);
    }
    
    /**
     * 检查是否启用了全局日志监听（Application类上有@Log注解）
     */
    private boolean isGlobalLogEnabled() {
        try {
            Class<?> applicationClass = Class.forName("org.example.demoApplication");
            return applicationClass.getAnnotation(Log.class) != null;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * 获取HTTP请求对象
     */
    private HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 填充请求信息
     */
    private void fillRequestInfo(LogInfo logInfo, HttpServletRequest request) {
        logInfo.setRequestTime(LocalDateTime.now());
        logInfo.setIp(getClientIpAddress(request));
        logInfo.setPort(request.getServerPort());
        logInfo.setMethod(request.getMethod());
        logInfo.setUrl(request.getRequestURL().toString());
        logInfo.setUserAgent(request.getHeader("User-Agent"));
        
        // 尝试从请求中获取用户名（可以根据实际情况调整）
        String username = request.getParameter("username");
        if (username == null) {
            username = request.getHeader("username");
        }
        logInfo.setUsername(username);
    }
    
    /**
     * 填充方法信息
     */
    private void fillMethodInfo(LogInfo logInfo, ProceedingJoinPoint joinPoint, Log logAnnotation) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        logInfo.setClassName(joinPoint.getTarget().getClass().getSimpleName());
        logInfo.setMethodName(signature.getName());
        
        if (logAnnotation != null && !logAnnotation.value().isEmpty()) {
            logInfo.setDescription(logAnnotation.value());
        }
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
}
