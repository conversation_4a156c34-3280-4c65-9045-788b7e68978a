package org.example;
public class Main {
    public static void main(String[] args) {

        int[] nums = {1, 1, 2};
        Boolean isAdd = true;
        int index = 0;
        for(int i=0; i< nums.length; i++){
            for(int j = i+ 1; j< nums.length;j++){
                if(nums[j] > nums[i]){
                    int tem = nums[i + 1];
                    nums[i + 1] = nums[j];
                    nums[j] = tem;
                    break;
                }
            }
            if(i < nums.length - 1 && nums[i] < nums[i + 1] && isAdd){
                index ++;
            }
            else {
                isAdd = false;
            }
        }
        System.out.println(nums[index]);
    }
}