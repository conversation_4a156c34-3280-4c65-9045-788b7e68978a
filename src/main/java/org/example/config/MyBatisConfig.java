package org.example.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.example.interceptor.SqlInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * MyBatis配置类
 */
@Configuration
@MapperScan("org.example.mapper")
public class MyBatisConfig {
    
    @Autowired
    private SqlSessionFactory sqlSessionFactory;
    
    @Autowired
    private SqlInterceptor sqlInterceptor;
    
    @PostConstruct
    public void addInterceptor() {
        sqlSessionFactory.getConfiguration().addInterceptor(sqlInterceptor);
    }
}
