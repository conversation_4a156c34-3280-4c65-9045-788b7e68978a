package org.example.monitor;

import org.example.entity.SqlQueryInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * SQL监控器
 * 使用ThreadLocal存储当前线程的SQL执行信息
 */
public class SqlMonitor {
    
    private static final ThreadLocal<List<SqlQueryInfo>> SQL_QUERIES = new ThreadLocal<>();
    private static final ThreadLocal<Boolean> MONITORING_ENABLED = new ThreadLocal<>();
    
    /**
     * 启用SQL监控
     */
    public static void enable() {
        MONITORING_ENABLED.set(true);
        SQL_QUERIES.set(new ArrayList<>());
    }
    
    /**
     * 禁用SQL监控
     */
    public static void disable() {
        MONITORING_ENABLED.remove();
        SQL_QUERIES.remove();
    }
    
    /**
     * 检查是否启用了监控
     */
    public static boolean isEnabled() {
        return Boolean.TRUE.equals(MONITORING_ENABLED.get());
    }
    
    /**
     * 开始记录SQL查询
     */
    public static SqlQueryInfo startQuery(String sql, String parameters) {
        if (!isEnabled()) {
            return null;
        }
        
        SqlQueryInfo queryInfo = new SqlQueryInfo();
        queryInfo.setSql(sql);
        queryInfo.setParameters(parameters);
        
        // 分析SQL类型
        String upperSql = sql.trim().toUpperCase();
        if (upperSql.startsWith("SELECT")) {
            queryInfo.setQueryType("SELECT");
        } else if (upperSql.startsWith("INSERT")) {
            queryInfo.setQueryType("INSERT");
        } else if (upperSql.startsWith("UPDATE")) {
            queryInfo.setQueryType("UPDATE");
        } else if (upperSql.startsWith("DELETE")) {
            queryInfo.setQueryType("DELETE");
        } else {
            queryInfo.setQueryType("OTHER");
        }
        
        return queryInfo;
    }
    
    /**
     * 结束记录SQL查询
     */
    public static void endQuery(SqlQueryInfo queryInfo, Integer affectedRows) {
        if (queryInfo == null || !isEnabled()) {
            return;
        }
        
        queryInfo.setAffectedRows(affectedRows);
        queryInfo.markEnd();
        
        List<SqlQueryInfo> queries = SQL_QUERIES.get();
        if (queries != null) {
            queries.add(queryInfo);
        }
    }
    
    /**
     * 记录SQL查询异常
     */
    public static void recordError(SqlQueryInfo queryInfo, String errorMessage) {
        if (queryInfo == null || !isEnabled()) {
            return;
        }
        
        queryInfo.markError(errorMessage);
        
        List<SqlQueryInfo> queries = SQL_QUERIES.get();
        if (queries != null) {
            queries.add(queryInfo);
        }
    }
    
    /**
     * 获取当前线程的所有SQL查询信息
     */
    public static List<SqlQueryInfo> getQueries() {
        List<SqlQueryInfo> queries = SQL_QUERIES.get();
        return queries != null ? new ArrayList<>(queries) : new ArrayList<>();
    }
    
    /**
     * 获取总SQL执行时间
     */
    public static long getTotalSqlTime() {
        List<SqlQueryInfo> queries = getQueries();
        return queries.stream()
                .mapToLong(q -> q.getExecutionTime() != null ? q.getExecutionTime() : 0L)
                .sum();
    }
    
    /**
     * 获取SQL查询总数
     */
    public static int getSqlCount() {
        return getQueries().size();
    }
    
    /**
     * 清空当前线程的SQL查询记录
     */
    public static void clear() {
        List<SqlQueryInfo> queries = SQL_QUERIES.get();
        if (queries != null) {
            queries.clear();
        }
    }
}
