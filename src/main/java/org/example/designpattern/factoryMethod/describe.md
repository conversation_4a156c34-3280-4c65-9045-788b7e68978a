## 工厂模式（Factory Method）
创建型设计模式 （CreationPatterns）

工厂模式（Factory Method）是软件设计中的一种创建型模式，其本质在于将对象的创建过程与使用过程分离，通过定义一个接口或抽象类来让子类决定实例化哪一个类。工厂模式的引入不仅提升了系统的可扩展性和灵活性，还避免了对象创建过程中的代码重复和耦合，成为面向对象设计的重要组成部分。

在开发软件时，我们常常面临这样的困境：系统中需要创建多种不同类型的对象，而这些对象的创建逻辑复杂且可能频繁变化。如果直接在代码中硬编码对象的创建逻辑，当需求发生变化时，整个系统的代码将难以维护。有没有一种方法可以让我们轻松扩展对象类型，而无需修改核心逻辑呢？工厂模式便是为了解决这一问题而生的，它通过将对象的创建过程抽象化，从根本上改善代码的灵活性和可维护性。

![img.png](img.png)
![img_1.png](img_1.png)

1. 工厂模式的定义和本质

1.1 定义

工厂模式是一种用于创建对象的设计模式，它定义了一个创建对象的接口，但由子类决定实例化哪一个具体类。这样，工厂方法将类的实例化延迟到了子类中。

1.2 工厂模式的本质

工厂模式的本质是封装变化，将具体类的创建过程封装起来，通过抽象工厂提供统一的接口，从而实现对扩展开放、对修改关闭的设计原则（即开闭原则）。

2. 为什么引入工厂模式

2.1 减少耦合性

直接在代码中创建对象会导致高耦合，这种耦合性在系统需要扩展时将非常脆弱。工厂模式通过抽象化对象的创建过程，使得客户端代码只依赖于抽象，而不依赖于具体实现，从而降低了耦合性。

2.2 提高可扩展性

当需要增加新的产品类型时，只需创建新的具体工厂类和具体产品类，而无需修改现有的工厂和产品接口。

2.3 遵循开闭原则

工厂模式的设计使得系统对扩展开放，对修改关闭。通过新增子类的方式可以轻松扩展新的功能。

2.4 统一对象的创建逻辑

在大型系统中，对象的创建可能非常复杂，需要遵循一定的规则或约束条件。工厂模式将这些创建逻辑集中在工厂中，方便统一管理和修改。

https://baijiahao.baidu.com/s?id=1818312671388176435&wfr=spider&for=pc

#### 普通工厂模式
```java


```



### 待梳理
1、建造者模式
2、WeakReference

































