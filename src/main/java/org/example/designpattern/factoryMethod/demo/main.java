package org.example.designpattern.factoryMethod.demo;

public class main {
    public static void main(String[] args) {

        // 普通工厂模式
        GeneralDocumentFactory generalDocumentFactory = new GeneralDocumentFactory();
        generalDocumentFactory.create("txt");
        
        // 多方法工厂模式
        MultiDocumentFactory multiDocumentFactory = new MultiDocumentFactory();
        multiDocumentFactory.createTxtDocument();

        // 静态方法工厂模式
        Document txtDocument = StaticDocumentFactory.createTxtDocument();

        // 抽象工厂模式
        AbstractDocumentFactory documentFactory = new TextDocumentFactory();
        Document document = documentFactory.createDocument();

    }
}
